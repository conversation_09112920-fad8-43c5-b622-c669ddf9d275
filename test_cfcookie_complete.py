#!/usr/bin/env python3
"""
CF Cookie 完整测试脚本
测试获取cf_clearance cookie并验证其有效性
"""

import requests
import json
import time
import sys

def test_cfcookie(url):
    """测试cfcookie功能"""
    print(f"🚀 开始测试 cfcookie 功能")
    print(f"目标网站: {url}")
    print("-" * 50)
    
    # 1. 获取cf_clearance cookie
    print("1️⃣ 获取 cf_clearance cookie...")
    
    try:
        response = requests.post('http://localhost:3000/', 
            json={
                "type": "cfcookie",
                "websiteUrl": url
            },
            timeout=120
        )
        
        result = response.json()
        
        if result.get('code') == 200 and 'cf_clearance' in result:
            cookie = result['cf_clearance']
            print(f"✅ 成功获取 cf_clearance cookie")
            print(f"   长度: {len(cookie)} 字符")
            print(f"   前缀: {cookie[:50]}...")
            
            # 2. 测试使用cookie访问网站
            print(f"\n2️⃣ 测试使用cookie访问目标网站...")
            
            # 构建更完整的请求头
            headers = {
                'Cookie': f'cf_clearance={cookie}',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0'
            }
            
            try:
                site_response = requests.get(url, headers=headers, timeout=15)
                print(f"   HTTP状态码: {site_response.status_code}")
                print(f"   响应长度: {len(site_response.text)} 字符")
                
                # 检查响应内容
                content = site_response.text.lower()
                
                # 检查Cloudflare验证页面的标识
                cf_indicators = [
                    'just a moment',
                    'cf-browser-verification',
                    'checking if the site connection is secure',
                    'ddos protection by cloudflare',
                    'ray id:',
                    'cloudflare',
                    'cf-challenge'
                ]
                
                has_cf_challenge = any(indicator in content for indicator in cf_indicators)
                
                if has_cf_challenge:
                    print("   ⚠️  响应中仍包含Cloudflare验证页面")
                    
                    # 尝试找到具体的验证类型
                    if 'just a moment' in content:
                        print("   类型: JavaScript验证页面")
                    elif 'cf-browser-verification' in content:
                        print("   类型: 浏览器验证页面")
                    elif 'ray id:' in content:
                        print("   类型: Cloudflare错误页面")
                else:
                    print("   ✅ 成功绕过Cloudflare保护！")
                    
                    # 检查页面内容是否正常
                    if len(site_response.text) > 1000:
                        print("   📄 页面内容丰富，可能是正常页面")
                    
                    # 查找页面标题
                    import re
                    title_match = re.search(r'<title[^>]*>([^<]+)</title>', site_response.text, re.IGNORECASE)
                    if title_match:
                        print(f"   📝 页面标题: {title_match.group(1).strip()}")
                
                # 3. 检查响应头
                print(f"\n3️⃣ 响应头信息:")
                important_headers = ['server', 'cf-ray', 'cf-cache-status', 'set-cookie']
                for header in important_headers:
                    if header in site_response.headers:
                        value = site_response.headers[header]
                        if header == 'set-cookie':
                            # 只显示cookie名称，不显示完整值
                            cookies = [c.split('=')[0] for c in value.split(';') if '=' in c]
                            print(f"   {header}: {', '.join(cookies[:3])}...")
                        else:
                            print(f"   {header}: {value}")
                
                return True
                
            except requests.exceptions.RequestException as e:
                print(f"   ❌ 访问网站失败: {e}")
                return False
                
        else:
            print("❌ 获取cf_clearance失败")
            print(f"响应: {result}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求cfcookie服务失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ 解析响应JSON失败: {e}")
        return False

def main():
    """主函数"""
    test_url = "https://loyalty.campnetwork.xyz/home"
    
    print("🧪 CF Clearance Scraper - cfcookie功能测试")
    print("=" * 60)
    
    # 检查服务是否运行
    try:
        health_response = requests.get('http://localhost:3000/health', timeout=5)
        if health_response.status_code == 200:
            print("✅ 服务运行正常")
        else:
            print("❌ 服务状态异常")
            return
    except:
        print("❌ 无法连接到服务，请确保服务正在运行")
        return
    
    print()
    
    # 执行测试
    success = test_cfcookie(test_url)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 cfcookie功能测试完成")
    else:
        print("❌ cfcookie功能测试失败")
    
    # 显示监控信息
    try:
        monitor_response = requests.get('http://localhost:3000/api/monitor', timeout=5)
        if monitor_response.status_code == 200:
            monitor_data = monitor_response.json()
            print(f"\n📊 服务监控信息:")
            print(f"   总请求: {monitor_data['requests']['total']}")
            print(f"   成功率: {monitor_data['requests']['successRate']}")
            print(f"   活跃请求: {len(monitor_data.get('activeRequests', []))}")
    except:
        pass

if __name__ == "__main__":
    main()
