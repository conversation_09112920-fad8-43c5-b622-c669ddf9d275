# =================================================================
# CF Clearance Scraper 统一配置文件
# =================================================================

# =================================================================
# 服务基础配置
# =================================================================

# 服务端口
PORT=3000

# 认证令牌 (可选，设置后所有API请求需要提供authToken)
# AUTH_TOKEN=your_secret_token

# 请求超时时间 (毫秒)
TIMEOUT=300000

# 内存管理配置
MEMORY_CLEANUP_INTERVAL=300000
MAX_MEMORY_USAGE=2048

# 最大并发请求数
MAX_CONCURRENT_REQUESTS=100

# 浏览器实例限制
BROWSER_LIMIT=25

# =================================================================
# 日志配置
# =================================================================

# 全局日志级别
LOG_LEVEL=CRITICAL

# =================================================================
# 监控配置
# =================================================================

# 内存监控间隔 (毫秒)
MEMORY_MONITOR_INTERVAL=30000

# 保留的最近token数量
MAX_RECENT_TOKENS=50

# 保留的请求历史数量
MAX_REQUEST_HISTORY=100

# =================================================================
# 代理配置 (可选)
# =================================================================

# HTTP代理设置
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080
# NO_PROXY=localhost,127.0.0.1,10.0.0.0/8,**********/12,***********/16

# =================================================================
# 配置说明
# =================================================================

# 1. 端口配置: 修改PORT可改变服务监听端口
# 2. 性能调优: 调整TIMEOUT、MAX_MEMORY_USAGE、BROWSER_LIMIT以适应服务器性能
# 3. 安全设置: 设置AUTH_TOKEN启用API认证
# 4. 日志控制: LOG_LEVEL=CRITICAL可减少输出，提高性能
# 5. 监控配置: 调整MAX_RECENT_TOKENS和MAX_REQUEST_HISTORY控制内存使用

# =================================================================
# 快速配置模板
# =================================================================

# 开发环境 (低并发，详细日志):
# BROWSER_LIMIT=10
# LOG_LEVEL=INFO
# NODE_ENV=development

# 生产环境 (高并发，最小日志):
# BROWSER_LIMIT=25
# LOG_LEVEL=CRITICAL
# NODE_ENV=production

# 轻量环境 (极低并发，节省资源):
# BROWSER_LIMIT=5
# MAX_MEMORY_USAGE=256
# MAX_CONCURRENT_REQUESTS=20
