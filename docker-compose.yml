version: '3.8'

services:
  cf-clearance-scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cf-clearance-scraper
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DOCKER=true
      - HEADLESS=false
      - TIMEOUT=120000
      - SKIP_LAUNCH=false
      - PUPPETEER_EXECUTABLE_PATH=/home/<USER>/.cache/puppeteer/chrome/linux-131.0.6778.204/chrome-linux64/chrome
      - CHROME_PATH=/home/<USER>/.cache/puppeteer/chrome/linux-131.0.6778.204/chrome-linux64/chrome
      - DISPLAY=:99
      # 可选：设置认证令牌
      # - AUTH_TOKEN=your_secret_token
    volumes:
      # 可选：持久化日志
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    # 安全设置
    security_opt:
      - no-new-privileges:true
    # 网络设置
    networks:
      - cf-scraper-network

networks:
  cf-scraper-network:
    driver: bridge
