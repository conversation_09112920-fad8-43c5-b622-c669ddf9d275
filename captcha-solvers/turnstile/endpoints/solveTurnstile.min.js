const fs = require("fs");
function solveTurnstileMin({ url, proxy, siteKey }) {
  return new Promise(async (resolve, reject) => {
    if (!url) return reject("Missing url parameter");
    if (!siteKey) return reject("Missing siteKey parameter");

    // 检查浏览器是否已初始化
    if (!global.browser) {
      if (global.browserInitFailed) {
        return reject("浏览器初始化失败，请检查Chrome安装和配置");
      }
      return reject("浏览器正在初始化中，请稍后重试");
    }

    let context = null;
    let contextClosed = false;

    try {
      // 使用上下文池获取上下文
      if (global.contextPool && typeof global.contextPool.getContext === 'function') {
        context = await global.contextPool.getContext();
      } else {
        // 回退到直接创建
        context = await global.browser
          .createBrowserContext({
            proxyServer: proxy ? `http://${proxy.host}:${proxy.port}` : undefined,
          })
          .catch(() => null);
      }

      if (!context) return reject("Failed to create browser context");

      let isResolved = false;

      const closeContext = async () => {
        if (!contextClosed && context) {
          try {
            contextClosed = true;
            // 使用上下文池释放上下文
            if (global.contextPool && typeof global.contextPool.releaseContext === 'function') {
              await global.contextPool.releaseContext(context);
            } else {
              // 回退到直接关闭
              await context.close();
            }
          } catch (err) {
            console.error("Error releasing context:", err.message);
          }
        }
      };

      var cl = setTimeout(async () => {
        if (!isResolved) {
          await closeContext();
          reject("Timeout Error");
        }
      }, global.timeOut || 180000);
      const page = await context.newPage();

      if (proxy?.username && proxy?.password)
        await page.authenticate({
          username: proxy.username,
          password: proxy.password,
        });

      await page.setRequestInterception(true);

      page.on("request", async (request) => {
        if (
          [url, url + "/"].includes(request.url()) &&
          request.resourceType() === "document"
        ) {
          await request.respond({
            status: 200,
            contentType: "text/html",
            body: String(
              require("fs").readFileSync(require("path").join(__dirname, "../data/fakePage.html"))
            ).replace(/<site-key>/g, siteKey),
          });
        } else {
          await request.continue();
        }
      });

      // 优化页面加载和token等待
      await page.goto(url, {
        waitUntil: "domcontentloaded",
        timeout: 30000
      });

      // 优化的token等待逻辑，减少阻塞时间
      let token = null;
      const maxAttempts = 2; // 减少重试次数，提高并发
      const baseTimeout = 45000; // 减少基础超时到45秒
      
      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
          console.log(`🔄 Turnstile attempt ${attempt}/${maxAttempts} for ${new URL(url).hostname}`);
          
          // 并行等待Turnstile脚本和token
          const tokenPromise = page.waitForSelector('[name="cf-response"]', {
            timeout: baseTimeout + (attempt * 5000) // 较小的递增时间
          });
          
          const scriptPromise = page.waitForFunction(() => {
            return window.turnstile && typeof window.turnstile.render === 'function';
          }, { timeout: 10000 }).catch(() => {
            console.log('⚠️  Turnstile script not loaded quickly, continuing...');
          });
          
          // 并行等待，提高效率
          await Promise.race([
            tokenPromise,
            scriptPromise.then(() => tokenPromise)
          ]);
          
          // 快速验证token
          token = await page.evaluate(() => {
            const element = document.querySelector('[name="cf-response"]');
            const value = element?.value;
            
            // 优化的token验证
            if (value && value.length > 10 && 
                !value.includes('undefined') && 
                !value.includes('null') &&
                value.indexOf('.') > 0) { // Turnstile token通常包含点
              return value;
            }
            return null;
          });
          
          if (token) {
            console.log(`✅ Turnstile token obtained on attempt ${attempt} (${token.length} chars)`);
            break;
          } else {
            console.log(`❌ Invalid token on attempt ${attempt}, retrying...`);
            if (attempt < maxAttempts) {
              // 更短的等待时间
              await new Promise(resolve => setTimeout(resolve, 1000));
              // 快速刷新
              await page.reload({ waitUntil: 'domcontentloaded', timeout: 15000 });
            }
          }
          
        } catch (attemptError) {
          console.log(`❌ Attempt ${attempt} failed: ${attemptError.message}`);
          if (attempt === maxAttempts) {
            throw attemptError;
          }
          // 更快的失败恢复
          await page.reload({ waitUntil: 'domcontentloaded', timeout: 15000 }).catch(() => {});
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }
      
      isResolved = true;
      clearTimeout(cl);
      await closeContext();
      if (!token || token.length < 10) return reject("Failed to get token");
      return resolve(token);
      
    } catch (e) {
      console.log(e);
      isResolved = true;
      clearTimeout(cl);
      if (context && !contextClosed) {
        try {
          contextClosed = true;
          await context.close();
        } catch (err) {
          console.error("Error closing context:", err.message);
        }
      }
      reject(e.message);
    }
  });
}
module.exports = solveTurnstileMin;