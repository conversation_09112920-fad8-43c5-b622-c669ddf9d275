#!/usr/bin/env python3
"""
测试修复后的cfcookie功能
验证：
1. 上下文池缓存问题已修复（每次请求返回不同的cookie）
2. 完整的请求头信息已包含在响应中
"""

import requests
import json
import time

def test_multiple_cfcookie_requests():
    """测试多次cfcookie请求，验证缓存问题已修复"""
    print("🔧 测试修复后的cfcookie功能")
    print("=" * 60)
    
    url = "https://loyalty.campnetwork.xyz/home"
    results = []
    
    # 进行3次请求
    for i in range(3):
        print(f"\n🚀 第{i+1}次cfcookie请求...")
        
        try:
            response = requests.post('http://localhost:3000/', 
                json={
                    "type": "cfcookie",
                    "websiteUrl": url
                },
                timeout=120
            )
            
            result = response.json()
            
            if result.get('code') == 200:
                print(f"✅ 请求{i+1}成功")
                
                # 检查新的响应格式
                if 'cf_clearance' in result:
                    cookie = result['cf_clearance']
                    print(f"   cf_clearance: {cookie[:50]}...")
                    
                    # 检查是否包含headers
                    if 'headers' in result:
                        headers = result['headers']
                        print(f"   ✅ 包含完整请求头 ({len(headers)}个)")
                        print(f"   User-Agent: {headers.get('User-Agent', 'N/A')[:50]}...")
                    else:
                        print("   ❌ 缺少请求头信息")
                    
                    # 检查是否包含所有cookies
                    if 'cookies' in result:
                        cookies = result['cookies']
                        print(f"   ✅ 包含所有cookies ({len(cookies)}个)")
                        cookie_names = [c['name'] for c in cookies]
                        print(f"   Cookie名称: {', '.join(cookie_names[:5])}...")
                    else:
                        print("   ❌ 缺少完整cookie信息")
                    
                    # 检查时间戳
                    if 'timestamp' in result:
                        print(f"   ✅ 包含时间戳: {result['timestamp']}")
                    
                    results.append({
                        'cf_clearance': cookie,
                        'timestamp': result.get('timestamp'),
                        'headers': result.get('headers', {}),
                        'cookies': result.get('cookies', [])
                    })
                else:
                    print(f"   ❌ 请求{i+1}失败：缺少cf_clearance")
                    print(f"   响应: {result}")
            else:
                print(f"   ❌ 请求{i+1}失败：{result.get('message', 'Unknown error')}")
                
        except Exception as e:
            print(f"   ❌ 请求{i+1}异常：{e}")
        
        # 等待一下再进行下一次请求
        if i < 2:
            print("   ⏳ 等待5秒...")
            time.sleep(5)
    
    # 分析结果
    print(f"\n📊 结果分析:")
    print(f"成功请求数: {len(results)}")
    
    if len(results) >= 2:
        # 检查cookie是否不同
        unique_cookies = set(r['cf_clearance'] for r in results)
        print(f"唯一cookie数: {len(unique_cookies)}")
        
        if len(unique_cookies) == len(results):
            print("✅ 上下文缓存问题已修复：每次请求返回不同的cookie")
        elif len(unique_cookies) == 1:
            print("❌ 上下文缓存问题仍存在：所有请求返回相同的cookie")
        else:
            print("⚠️  部分请求返回相同的cookie")
        
        # 检查请求头格式
        headers_complete = all('headers' in r and len(r['headers']) > 5 for r in results)
        if headers_complete:
            print("✅ 请求头信息完整")
        else:
            print("❌ 请求头信息不完整")
        
        # 检查cookies信息
        cookies_complete = all('cookies' in r and len(r['cookies']) > 0 for r in results)
        if cookies_complete:
            print("✅ 完整cookie信息已包含")
        else:
            print("❌ 完整cookie信息缺失")
    
    return results

def test_cookie_usage(result):
    """测试使用获取的cookie和headers访问网站"""
    if not result or 'cf_clearance' not in result:
        print("❌ 没有有效的cookie进行测试")
        return False
    
    print(f"\n🌐 测试使用获取的cookie和headers访问网站...")
    
    try:
        # 使用获取的headers
        headers = result.get('headers', {})
        headers['Cookie'] = f"cf_clearance={result['cf_clearance']}"
        
        # 添加其他重要的cookies
        if 'cookies' in result:
            cookie_parts = [f"cf_clearance={result['cf_clearance']}"]
            for cookie in result['cookies']:
                if cookie['name'] != 'cf_clearance':
                    cookie_parts.append(f"{cookie['name']}={cookie['value']}")
            headers['Cookie'] = '; '.join(cookie_parts)
        
        target_url = result.get('url', 'https://loyalty.campnetwork.xyz/home')
        response = requests.get(target_url, headers=headers, timeout=15)
        
        print(f"   HTTP状态码: {response.status_code}")
        print(f"   响应长度: {len(response.text)} 字符")
        
        # 检查是否成功绕过Cloudflare
        content = response.text.lower()
        cf_indicators = [
            'just a moment',
            'cf-browser-verification',
            'checking if the site connection is secure'
        ]
        
        has_cf_challenge = any(indicator in content for indicator in cf_indicators)
        
        if not has_cf_challenge:
            print("   ✅ 成功绕过Cloudflare保护")
            return True
        else:
            print("   ⚠️  仍显示Cloudflare验证页面")
            return False
            
    except Exception as e:
        print(f"   ❌ 访问失败: {e}")
        return False

def main():
    print("🧪 CF Clearance Scraper - 修复验证测试")
    print("测试内容：")
    print("1. 上下文池缓存问题修复")
    print("2. 完整请求头信息返回")
    print("3. Cookie使用效果验证")
    print()
    
    # 测试多次请求
    results = test_multiple_cfcookie_requests()
    
    # 如果有成功的结果，测试cookie使用
    if results:
        test_cookie_usage(results[0])
    
    print(f"\n{'='*60}")
    print("🎯 测试总结:")
    
    if len(results) >= 2:
        unique_cookies = set(r['cf_clearance'] for r in results)
        if len(unique_cookies) == len(results):
            print("✅ 上下文缓存问题已修复")
        else:
            print("❌ 上下文缓存问题仍存在")
    
    if results and 'headers' in results[0] and len(results[0]['headers']) > 5:
        print("✅ 完整请求头信息已返回")
    else:
        print("❌ 请求头信息不完整")
    
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
