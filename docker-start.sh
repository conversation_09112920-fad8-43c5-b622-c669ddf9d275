#!/bin/bash

# CF Clearance Scraper Docker 启动脚本

set -e

echo "🐳 CF Clearance Scraper Docker 部署脚本"
echo "========================================"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    echo "   下载地址: https://www.docker.com/products/docker-desktop"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker 未运行，请启动 Docker Desktop"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

echo "✅ Docker 环境检查通过"

# 停止现有容器（如果存在）
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || docker compose down 2>/dev/null || true

# 构建并启动容器
echo "🔨 构建 Docker 镜像..."
if command -v docker-compose &> /dev/null; then
    docker-compose build --no-cache
    echo "🚀 启动服务..."
    docker-compose up -d
else
    docker compose build --no-cache
    echo "🚀 启动服务..."
    docker compose up -d
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:3000/health &> /dev/null; then
    echo "✅ 服务启动成功！"
    echo ""
    echo "📊 监控面板: http://localhost:3000/monitor"
    echo "🔧 API端点: http://localhost:3000/"
    echo "❤️  健康检查: http://localhost:3000/health"
    echo ""
    echo "📋 查看日志: docker-compose logs -f"
    echo "🛑 停止服务: docker-compose down"
else
    echo "❌ 服务启动失败，查看日志:"
    if command -v docker-compose &> /dev/null; then
        docker-compose logs
    else
        docker compose logs
    fi
    exit 1
fi
