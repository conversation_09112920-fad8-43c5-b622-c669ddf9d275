# 监控面板使用说明

## 🎯 功能特性

✅ **实时监控** - 每3秒自动刷新数据  
✅ **服务状态** - 运行时间、实例使用情况  
✅ **请求统计** - 总请求数、成功率、活跃请求  
✅ **内存监控** - 堆内存、RSS、系统内存使用  
✅ **Token记录** - 最近生成的Token列表  
✅ **请求历史** - 最近请求的详细记录  
✅ **性能指标** - 平均响应时间、请求频率  
✅ **实时图表** - 请求趋势可视化  

## 🚀 访问方式

启动CF Clearance Scraper服务后，访问：

```
http://localhost:3000/monitor/
```

如果是局域网部署，使用服务器IP：

```
http://************:3000/monitor/
```

## 📊 监控指标说明

### 服务状态
- **运行时间**: 服务启动后的总运行时间
- **启动时间**: 服务的启动时间戳

### 实例信息
- **总实例数**: 配置的最大并发实例数
- **活跃实例**: 当前正在处理请求的实例数
- **可用实例**: 剩余可用的实例数
- **使用率**: 实例使用百分比
- **浏览器上下文**: 当前活跃的浏览器上下文数量

### 请求统计
- **总请求数**: 服务启动后处理的总请求数
- **成功请求**: 成功完成的请求数
- **失败请求**: 失败的请求数
- **活跃请求**: 当前正在处理的请求数
- **成功率**: 请求成功的百分比

### 内存信息
- **堆内存使用**: Node.js堆内存使用量
- **RSS内存**: 进程实际占用的物理内存
- **系统已用**: 系统总内存使用量
- **系统可用**: 系统可用内存量

### 性能指标
- **平均响应时间**: 最近请求的平均处理时间
- **每分钟请求数**: 最近一分钟的请求频率

## 🔧 功能操作

### 重置数据
点击"重置数据"按钮可以清空所有监控统计数据，重新开始计数。

### 自动刷新
监控面板每3秒自动刷新一次数据，确保信息的实时性。

### 页面离开暂停
当浏览器标签页不可见时，自动暂停数据刷新以节省资源。

## 📱 响应式设计

监控面板支持：
- 桌面端浏览器
- 平板设备
- 手机浏览器

自动适配不同屏幕尺寸，确保在各种设备上都有良好的显示效果。

## 🎨 界面特性

- **实时指示器**: 显示数据更新状态
- **渐变背景**: 现代化的视觉设计
- **卡片布局**: 清晰的信息分组
- **进度条**: 直观的使用率展示
- **状态图标**: 快速识别服务状态
- **实时图表**: 请求趋势可视化

## 🚨 故障排除

### 无法访问监控面板
1. 确认CF Clearance Scraper服务已启动
2. 检查端口3000是否被占用
3. 确认防火墙设置允许访问

### 数据不更新
1. 检查浏览器控制台是否有错误
2. 确认服务API端点正常工作
3. 尝试刷新页面

### 图表不显示
1. 确认浏览器支持Canvas
2. 检查JavaScript是否被禁用
3. 尝试使用其他浏览器

## 📈 监控最佳实践

1. **定期检查**: 建议每天查看一次监控数据
2. **关注趋势**: 重点关注成功率和响应时间趋势
3. **资源监控**: 密切关注内存使用情况
4. **性能优化**: 根据监控数据调整配置参数