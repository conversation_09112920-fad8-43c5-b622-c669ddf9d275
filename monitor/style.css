* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    animation: slideInUp 0.6s ease-out;
}

.card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.card h3 .icon {
    margin-right: 8px;
    font-size: 1.4rem;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255,255,255,0.7);
    border-radius: 8px;
    border: 1px solid rgba(0,0,0,0.1);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.85rem;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-running { background-color: #27ae60; }
.status-warning { background-color: #f39c12; }
.status-error { background-color: #e74c3c; }

.progress-bar {
    background: rgba(0,0,0,0.1);
    border-radius: 10px;
    height: 8px;
    margin-top: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.table-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

th {
    background: rgba(0,0,0,0.05);
    font-weight: 600;
    color: #2c3e50;
}

.btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: transform 0.2s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.token-container {
    max-height: 400px;
    overflow-y: auto;
}

.token-item {
    background: rgba(0,0,0,0.05);
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
    font-family: monospace;
    font-size: 0.8rem;
    word-break: break-all;
}

.timestamp {
    color: #7f8c8d;
    font-size: 0.75rem;
    margin-top: 5px;
}

.loading {
    text-align: center;
    color: #7f8c8d;
    padding: 20px;
}

.error {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    padding: 15px;
    border-radius: 6px;
    margin: 10px 0;
}


.metric-item {
    background: rgba(255,255,255,0.7);
    padding: 10px 15px;
    border-radius: 8px;
    text-align: center;
    min-width: 120px;
}

.metric-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 5px;
}

.metric-value {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

.footer a {
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    margin: 0 5px;
}

.footer a:hover {
    color: white;
}

.footer img {
    width: 16px;
    height: 16px;
    margin-right: 5px;
}

.chart-container {
    width: 100%;
    position: relative;
    height: 300px;
}

.load-chart-container {
    width: 100%;
    position: relative;
    height: 150px;
}

.chart-canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
}

/* 负载历史卡片样式 */
.load-history-card {
    height: auto;
    max-height: 300px;
}

/* 请求记录表格容器限制高度 */
.table-container {
    max-height: 500px;
    overflow-y: auto;
}

.table-container table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

/* 表格滚动样式 */
.table-container::-webkit-scrollbar {
    width: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.6);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.8);
}

/* 实时指示器 */
.live-indicator {
    display: inline-flex;
    align-items: center;
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-left: 10px;
}

.live-indicator::before {
    content: '●';
    margin-right: 4px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 性能指标卡片 */
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: rotate(45deg);
    pointer-events: none;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0.3px;
    border: 1px solid transparent;
}

.status-success {
    background: rgba(39, 174, 96, 0.15);
    color: #27ae60;
    border-color: rgba(39, 174, 96, 0.3);
}

.status-warning {
    background: rgba(243, 156, 18, 0.15);
    color: #f39c12;
    border-color: rgba(243, 156, 18, 0.3);
}

.status-error {
    background: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
    border-color: rgba(231, 76, 60, 0.3);
}

.status-running {
    background: rgba(39, 174, 96, 0.15);
    color: #27ae60;
    border-color: rgba(39, 174, 96, 0.3);
}

/* 活跃请求行样式 */
.active-request {
    animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    from {
        background-color: rgba(255, 193, 7, 0.05);
    }
    to {
        background-color: rgba(255, 193, 7, 0.15);
    }
}

/* 滚动条美化 */
.token-container::-webkit-scrollbar,
.table-container::-webkit-scrollbar {
    width: 6px;
}

.token-container::-webkit-scrollbar-track,
.table-container::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 3px;
}

.token-container::-webkit-scrollbar-thumb,
.table-container::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.3);
    border-radius: 3px;
}

.token-container::-webkit-scrollbar-thumb:hover,
.table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.5);
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 仪表盘样式 */
.gauge-container {
    display: inline-block;
    position: relative;
}

.gauge {
    transform: rotate(0deg);
    transition: all 0.5s ease-in-out;
}

.gauge circle:last-of-type {
    transition: stroke-dashoffset 1s ease-in-out;
}

/* CPU 仪表盘颜色 */
.gauge circle:last-of-type[stroke="#3498db"] {
    stroke: #3498db;
}

/* 内存仪表盘颜色 */
.gauge circle:last-of-type[stroke="#e74c3c"] {
    stroke: #e74c3c;
}

/* 根据使用率调整颜色 */
.gauge-high-usage circle:last-of-type {
    stroke: #e74c3c !important;
}

.gauge-medium-usage circle:last-of-type {
    stroke: #f39c12 !important;
}

.gauge-low-usage circle:last-of-type {
    stroke: #27ae60 !important;
}

@media (max-width: 768px) {
    .dashboard {
        grid-template-columns: 1fr;
    }
    
    .stat-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    /* 移动端仪表盘调整 */
    .gauge {
        width: 100px;
        height: 100px;
    }
    
    .gauge text {
        font-size: 14px;
    }
}

/* 实时图表样式 */
.chart-card {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 12px;
    height: 120px;
    margin-bottom: 12px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.chart-title {
    font-size: 0.85rem;
    font-weight: 500;
}

.chart-value {
    font-size: 0.9rem;
    font-weight: bold;
}

.chart-canvas {
    width: 100%;
    height: 80px;
    display: block;
}

/* 青色系颜色定义 */
.cpu-color {
    color: #00ffff;
}

.memory-color {
    color: #00ffaa;
}

.heap-color {
    color: #00ddff;
}

/* 实时图表容器样式 */
.charts-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Canvas高清适配 */
canvas {
    image-rendering: -moz-crisp-edges;
    image-rendering: -webkit-crisp-edges;
    image-rendering: pixelated;
    image-rendering: crisp-edges;
}

/* 移动端图表调整 */
@media (max-width: 768px) {
    .chart-card {
        height: 100px;
        padding: 10px;
    }
    
    .chart-canvas {
        height: 60px;
    }
    
    .chart-title {
        font-size: 0.8rem;
    }
    
    .chart-value {
        font-size: 0.85rem;
    }
}