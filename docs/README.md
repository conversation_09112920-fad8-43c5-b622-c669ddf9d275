# CF Clearance Scraper 文档中心

专业的 Cloudflare 保护绕过工具，支持 Turnstile 令牌生成和 cf_clearance Cookie 获取。

## 📚 文档列表

- [🚀 快速开始](INSTALLATION.md) - 安装部署和快速上手
- [📖 使用教程](TUTORIAL.md) - 详细的使用指南和示例代码
- [📖 API 文档](API.md) - 完整的接口使用说明和示例
- [⚙️ 配置指南](CONFIGURATION.md) - 智能配置和性能优化
- [📊 性能优化](PERFORMANCE_OPTIMIZATION.md) - 智能内存管理和容量调节
- [🔧 故障排除](TROUBLESHOOTING.md) - 常见问题诊断和解决方案

## 🎯 核心功能

- **Cloudflare Turnstile 令牌生成** - 自动解决 Turnstile 验证
- **cf_clearance Cookie 获取** - 绕过 Cloudflare 保护页面
- **智能内存管理** - 根据系统资源自动调节内存限制
- **智能容量管理** - 根据 CPU 和内存自动调节并发数
- **实时监控面板** - Web 界面监控服务状态和性能

## 🚀 快速导航

- **新用户**: [安装指南](INSTALLATION.md) → [使用教程](TUTORIAL.md) → [API文档](API.md)
- **开发者**: [使用教程](TUTORIAL.md) → [API文档](API.md) → [配置指南](CONFIGURATION.md)
- **性能优化**: [配置指南](CONFIGURATION.md) → [性能优化](PERFORMANCE_OPTIMIZATION.md)
- **问题解决**: [故障排除](TROUBLESHOOTING.md)

## 💡 特色亮点

- ✅ **零配置启动** - 智能检测系统资源，自动优化配置
- ✅ **高性能** - 智能上下文池和内存管理
- ✅ **实时监控** - Web 监控界面，实时查看服务状态
- ✅ **跨平台支持** - Windows/macOS/Linux 一键部署
- ✅ **生产就绪** - 自动重启、错误恢复、性能监控