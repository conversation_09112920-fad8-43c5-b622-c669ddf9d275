# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 测试文件
test_*.py
tests/
*.test.js

# 日志文件
logs/
*.log

# 临时文件
.tmp/
temp/

# 开发工具
.vscode/
.idea/

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# 文档
docs/
README.md
LICENSE.md

# 脚本
scripts/
一键部署-*.command
一键部署-*.bat
start-*.command
start-*.bat

# 环境文件（如果包含敏感信息）
.env.local
.env.development
.env.test

# 其他
assets/
*.md
