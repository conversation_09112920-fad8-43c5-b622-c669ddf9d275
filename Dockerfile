FROM node:latest

RUN apt-get update && apt-get install -y --fix-missing \
    wget \
    gnupg \
    ca-certificates \
    apt-transport-https \
    chromium \
    chromium-driver \
    xvfb \
    && rm -rf /var/lib/apt/lists/* || \
    (apt-get update && apt-get install -y --fix-missing \
    wget gnupg ca-certificates chromium xvfb \
    && rm -rf /var/lib/apt/lists/*)

ENV CHROME_BIN=/usr/bin/chromium

WORKDIR /app

COPY package*.json ./

RUN npm update
RUN npm install

COPY . .

EXPOSE 3000

CMD ["node", "start.js"]
