FROM node:latest

RUN apt-get update && apt-get install -y --fix-missing \
    wget \
    gnupg \
    ca-certificates \
    apt-transport-https \
    chromium \
    chromium-driver \
    xvfb \
    && rm -rf /var/lib/apt/lists/* || \
    (apt-get update && apt-get install -y --fix-missing \
    wget gnupg ca-certificates chromium xvfb \
    && rm -rf /var/lib/apt/lists/*)

ENV CHROME_BIN=/usr/bin/chromium

WORKDIR /app

COPY package*.json ./

RUN npm update
RUN npm install

COPY . .

EXPOSE 3000

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 启动 Xvfb\n\
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &\n\
export DISPLAY=:99\n\
\n\
# 等待 Xvfb 启动\n\
sleep 3\n\
\n\
# 启动应用\n\
exec node start.js' > /app/start.sh && chmod +x /app/start.sh

CMD ["/app/start.sh"]
