# CF Clearance Scraper Docker Image
FROM ghcr.io/puppeteer/puppeteer:23.11.1

# 切换到root用户进行安装
USER root

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# 安装Node.js 18
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# 安装额外依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 创建非root用户
RUN groupadd -r cfuser && useradd -r -g cfuser -G audio,video cfuser \
    && mkdir -p /home/<USER>/Downloads \
    && chown -R cfuser:cfuser /home/<USER>
    && chown -R cfuser:cfuser /app

# 复制package文件
COPY package*.json ./

# 安装Node.js依赖
RUN npm ci --only=production && npm cache clean --force

# 复制应用代码
COPY . .

# 设置权限
RUN chown -R cfuser:cfuser /app

# 切换到非root用户
USER cfuser

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 启动命令
CMD ["node", "start.js"]
