#!/bin/bash

# CF Clearance Scraper Docker 测试脚本

set -e

echo "🧪 CF Clearance Scraper Docker 功能测试"
echo "========================================"

# 检查服务是否运行
echo "1️⃣ 检查服务状态..."
if ! curl -f http://localhost:3000/health &> /dev/null; then
    echo "❌ 服务未运行，请先启动: ./docker-start.sh"
    exit 1
fi
echo "✅ 服务运行正常"

# 测试 Turnstile 功能
echo ""
echo "2️⃣ 测试 Turnstile 令牌生成..."
TURNSTILE_RESPONSE=$(curl -s -X POST http://localhost:3000/ \
  -H "Content-Type: application/json" \
  -d '{
    "type": "cftoken",
    "websiteUrl": "https://turnstile.zeroclover.io/",
    "websiteKey": "0x4AAAAAAAEwzhD6pyKkgXC0"
  }')

TURNSTILE_CODE=$(echo "$TURNSTILE_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['code'])" 2>/dev/null || echo "error")

if [ "$TURNSTILE_CODE" = "200" ]; then
    TOKEN=$(echo "$TURNSTILE_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['token'][:50])" 2>/dev/null || echo "N/A")
    echo "✅ Turnstile 令牌生成成功"
    echo "   令牌前缀: ${TOKEN}..."
else
    echo "❌ Turnstile 令牌生成失败"
    echo "   响应: $TURNSTILE_RESPONSE"
fi

# 测试 cf_clearance 功能
echo ""
echo "3️⃣ 测试 cf_clearance Cookie 获取..."
CFCOOKIE_RESPONSE=$(curl -s -X POST http://localhost:3000/ \
  -H "Content-Type: application/json" \
  -d '{
    "type": "cfcookie",
    "websiteUrl": "https://loyalty.campnetwork.xyz/home"
  }')

CFCOOKIE_CODE=$(echo "$CFCOOKIE_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['code'])" 2>/dev/null || echo "error")

if [ "$CFCOOKIE_CODE" = "200" ]; then
    COOKIE=$(echo "$CFCOOKIE_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['cf_clearance'][:50])" 2>/dev/null || echo "N/A")
    HEADERS_COUNT=$(echo "$CFCOOKIE_RESPONSE" | python3 -c "import sys, json; print(len(json.load(sys.stdin).get('headers', {})))" 2>/dev/null || echo "0")
    COOKIES_COUNT=$(echo "$CFCOOKIE_RESPONSE" | python3 -c "import sys, json; print(len(json.load(sys.stdin).get('cookies', [])))" 2>/dev/null || echo "0")
    
    echo "✅ cf_clearance Cookie 获取成功"
    echo "   Cookie前缀: ${COOKIE}..."
    echo "   请求头数量: ${HEADERS_COUNT}"
    echo "   Cookies数量: ${COOKIES_COUNT}"
else
    echo "❌ cf_clearance Cookie 获取失败"
    echo "   响应: $CFCOOKIE_RESPONSE"
fi

# 测试监控API
echo ""
echo "4️⃣ 测试监控API..."
MONITOR_RESPONSE=$(curl -s http://localhost:3000/api/monitor)
MONITOR_STATUS=$(echo "$MONITOR_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['status'])" 2>/dev/null || echo "error")

if [ "$MONITOR_STATUS" = "running" ]; then
    TOTAL_REQUESTS=$(echo "$MONITOR_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['requests']['total'])" 2>/dev/null || echo "0")
    SUCCESS_RATE=$(echo "$MONITOR_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['requests']['successRate'])" 2>/dev/null || echo "0%")
    
    echo "✅ 监控API正常"
    echo "   总请求数: ${TOTAL_REQUESTS}"
    echo "   成功率: ${SUCCESS_RATE}"
else
    echo "❌ 监控API异常"
    echo "   响应: $MONITOR_RESPONSE"
fi

# 显示容器状态
echo ""
echo "5️⃣ Docker 容器状态..."
if command -v docker-compose &> /dev/null; then
    docker-compose ps
else
    docker compose ps
fi

# 显示资源使用
echo ""
echo "6️⃣ 容器资源使用..."
docker stats cf-clearance-scraper --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

echo ""
echo "🎉 测试完成！"
echo ""
echo "📊 监控面板: http://localhost:3000/monitor"
echo "📋 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"
